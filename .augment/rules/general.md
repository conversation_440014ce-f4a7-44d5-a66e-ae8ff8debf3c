---
type: "agent_requested"
description: "apply to all file"
---

# 基础开发规范

## 总体原则

- **满足业务需要**：代码必须实现业务功能
- **清晰明了**：让任何人都能看懂代码
- **尽可能简洁**：在保证清晰的前提下，代码越少越好
- **复用性和模块化**：提高代码的复用性和模块化程度

## 命名规范

### 英文单词要求

- 使用正确的英文单词，禁止使用拼音
- 使用常用词汇
- 命名要符合业务含义

### 合理区分名词和动词

- **名词**：用于类、接口、枚举、数据库表名等

  ```javascript
  // 正例
  class OrderService {}
  interface UserInfo {}
  ```

- **动词**：用于方法名
  ```javascript
  // 正例
  createOrder();
  queryUser();
  updateProfile();
  ```

### 函数命名规范

- 查询多条数据统一使用 `query` 前缀，分页表格使用 `Page` 后缀，列表使用 `List` 后缀
- 查询单条数据统一使用 `get` 前缀
- 新增数据统一使用 `add` 前缀
- 更新数据统一使用 `update` 前缀
- 删除数据统一使用 `delete` 前缀

## 注释规范

### 通用规范

- `<AUTHOR>
- `@Date` 当前时间
- 不要 `@Copyright`

### 方法注释

方法要尽量通过方法名自解释，不要写无用、信息冗余的方法头，不要写空有格式的方法头注释。

方法头注释内容可选，但不限于：功能说明、返回值，用法、算法实现等等。尤其是对外的方法接口声明，其注释，应当将重要、有用的信息表达清楚。

```javascript
/**
 * 解析转换时间字符串为 LocalDate 时间类
 * 调用前必须校验字符串格式，否则可能造成解析失败的错误异常
 *
 * @param {string} dateStr 必须是 yyyy-MM-dd 格式的字符串
 * @return {Date} LocalDate 对象
 */
function parseYMD(dateStr) {
  // 实现代码
}
```

### 逻辑分块注释

```javascript
function startLuckDraw(luckDrawDTO) {
  // -------------- 1、校验抽奖活动基本信息 ------------------------
  // -------------- 2、新增抽奖记录 -------------------------------
  // -------------- 3、如果需要消耗积分，则扣除积分 -----------------
  // -------------- 4、获取奖品信息，开始抽奖 ----------------------
}
```
