#############################################################################################################
#                                                                                                           #
# 为了减少重复配置，本配置文件为此soms-admin的独有配置，更多配置请查看 soms-base 项目中的 soms-base.yaml 通用配置文件。      #
# 其中此文件中配置可以覆盖 soms-base.yaml 中的通用配置，具体实现类请看类：YamlProcessor.java                          #
#                                                                                                           #
#############################################################################################################

# 项目配置: 名称、日志目录
project:
  name: soms-admin
  log-directory: /Volumes/Data/Temp/logs/soms/${project.name}/${spring.profiles.active}

# 项目端口和url根路径
server:
  port: 1024
  servlet:
    context-path: /

# 环境
spring:
  profiles:
    active: "@profiles.active@"

vmware:
  username: <EMAIL>
  password: Vcsa@clh9527!
  connection-timeout: 30000
  read-timeout: 60000
  session-cache-time: 3600
  clusters:
    VMWare-100: https://172.28.240.100
    VMWare-70: https://172.28.240.70
    VMWare-50: https://172.28.240.50
    VMWare-20: https://172.28.240.20
    财务虚拟化平台: https://172.28.240.80

cloudbility:
  # API基础URL
  base-url: https://base.xmut.edu.cn/api/openapi
  # 访问密钥ID
  access-key-id: ${CLOUDBILITY_ACCESS_KEY_ID:N1rGMdQVTGt4nDzE}
  # 访问密钥
  access-key-secret: ${CLOUDBILITY_ACCESS_KEY_SECRET:w4tzjdSsZyRJXb3DdtIIxEw637AvfV}
  # 团队ID
  team-id: ${CLOUDBILITY_TEAM_ID:182826779545600}
  # token过期时间（秒）
  token-expire-seconds: 3600
  # 请求超时时间（毫秒）
  timeout: 30000
