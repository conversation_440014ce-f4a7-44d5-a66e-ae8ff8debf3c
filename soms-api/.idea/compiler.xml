<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="soms-base" />
        <module name="soms-admin" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="tsa-parent" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="soms-admin" options="-parameters" />
      <module name="soms-base" options="-parameters" />
      <module name="soms-parent" options="-parameters" />
    </option>
  </component>
</project>